{"version": "0.2.0", "configurations": [{"name": "NLP.Api", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/src/NLP/RealPlusNLP.Api/bin/Debug/net9.0/RealPlusNLP.Api.dll", "args": [], "cwd": "${workspaceFolder}/src/NLP/RealPlusNLP.Api", "stopAtEntry": false, "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}}, {"name": "MediaSearch.Web", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/src/Media/RealPlusMediaSearch.Web/bin/Debug/net9.0/RealPlusMediaSearch.Web.dll", "args": [], "cwd": "${workspaceFolder}/src/Media/RealPlusMediaSearch.Web", "stopAtEntry": false, "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}}, {"name": "Similarity.Web", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/src/NLP/RealPlusNLP.Similarity.Web/bin/Debug/net9.0/RealPlusNLP.Similarity.Web.dll", "args": [], "cwd": "${workspaceFolder}/src/NLP/RealPlusNLP.Similarity.Web", "stopAtEntry": false, "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}}, {"name": "Similarity.<PERSON><PERSON>", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/src/NLP/RealPlusNLP.Similarity.Loader/bin/Debug/net9.0/RealPlusNLP.Similarity.Loader.dll", "args": [], "cwd": "${workspaceFolder}/src/NLP/RealPlusNLP.Similarity.Loader", "console": "internalConsole", "stopAtEntry": false}, {"name": "RealtimeForm.Web", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/src/Lab/RealtimeAI/RealtimeFormApp/bin/Debug/net9.0/RealtimeFormApp.dll", "args": [], "cwd": "${workspaceFolder}/src/Lab/RealtimeAI/RealtimeFormApp", "stopAtEntry": false, "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}}, {"name": "DBCahtPro.Web", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/src/Lab/ChatWithData/DBChatPro/bin/Debug/net9.0/DBChatPro.dll", "args": [], "cwd": "${workspaceFolder}/src/Lab/ChatWithData/DBChatPro", "stopAtEntry": false, "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}}]}