param location string
param env string
param appInsightsName string
param logAnalyticsName string
param name string

var isProduction = env == 'prod'

resource appInsights 'Microsoft.Insights/components@2020-02-02' existing = {
  name: appInsightsName
}

resource logAnalytics 'Microsoft.OperationalInsights/workspaces@2023-09-01' existing = {
  name: logAnalyticsName
}

resource containerAppEnvironment 'Microsoft.App/managedEnvironments@2024-10-02-preview' = {
  name: '${name}-${env}'
  location: location
  properties: {
    zoneRedundant: false
    appLogsConfiguration: {
      destination: 'log-analytics'
      logAnalyticsConfiguration: {
        customerId: logAnalytics.properties.customerId
        dynamicJsonColumns: true
        sharedKey: logAnalytics.listKeys().primarySharedKey
      }
    }
    workloadProfiles: isProduction
      ? [
          {
            name: 'Default'
            workloadProfileType: 'D4'
            minimumCount: 1
            maximumCount: 5
          }
        ]
      : null
    appInsightsConfiguration: {
      connectionString: appInsights.properties.ConnectionString
    }
    openTelemetryConfiguration: {
      tracesConfiguration: {
        destinations: [
          'appInsights'
        ]
      }
      logsConfiguration: {
        destinations: [
          'appInsights'
        ]
      }
      metricsConfiguration: {
        destinations: [
          'appInsights'
        ]
      }
    }
  }
}

output id string = containerAppEnvironment.id
output name string = containerAppEnvironment.name
output defaultDomain string = containerAppEnvironment.properties.defaultDomain
output staticIp string = containerAppEnvironment.properties.staticIp
