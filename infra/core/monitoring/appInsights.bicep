param location string
param env string
param logAnalyticsWorkspaceId string
param name string
@description('The Key Vault that this Application Insights workspace will use to store secrets in')
param keyVaultName string

resource keyVault 'Microsoft.KeyVault/vaults@2023-07-01' existing = {
  name: keyVaultName
}

resource appInsights 'Microsoft.Insights/components@2020-02-02' = {
  name: '${name}-${env}'
  location: location
  kind: 'web'
  properties: {
    Application_Type: 'web'
    WorkspaceResourceId: logAnalyticsWorkspaceId
    IngestionMode: 'LogAnalytics'
  }
}

resource appInsightsConnectionString 'Microsoft.KeyVault/vaults/secrets@2024-04-01-preview' = {
  name: 'app-insights-connection-string'
  parent: keyVault
  properties: {
    value: appInsights.properties.ConnectionString
  }
}

output id string = appInsights.id
output name string = appInsights.name
output connectionString string = appInsights.properties.ConnectionString
output instrumentationKey string = appInsights.properties.InstrumentationKey
