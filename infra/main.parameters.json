{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#", "contentVersion": "*******", "parameters": {"location": {"value": "${AZURE_LOCATION=eastus2}"}, "principalId": {"value": "${AZURE_PRINCIPAL_ID}"}, "env": {"value": "${AZURE_ENV_NAME=dev}"}, "logAnalyticsRetentionInDays": {"value": "${AZURE_LOG_ANALYTICS_RETENTION_IN_DAYS=30}"}, "openAiDeploymentCapacity": {"value": "${APP_OPEN_AI_DEPLOYMENT_CAPACITY=50}"}, "containerAppMinReplicas": {"value": "${APP_CONTAINER_APP_MIN_REPLICAS=1}"}, "containerAppMaxReplicas": {"value": "${APP_CONTAINER_APP_MAX_REPLICAS=5}"}, "containerAppRevisionMode": {"value": "${APP_CONTAINER_APP_REVISION_MODE=Single}"}, "logAnalyticsName": {"value": "log-ai-foundry"}, "appInsightsName": {"value": "appi-ai-foundry"}, "keyVaultName": {"value": "kv-ai-foundry"}, "openAiName": {"value": "ais-ai-foundry"}, "openAiCustomSubDomainName": {"value": "ai-foundry"}, "containerRegistryName": {"value": "craifoundry"}, "containerAppEnvName": {"value": "cae-ai-foundry"}, "containerAppNLPApiName": {"value": "ca-nlp-api"}, "containerAppNLPApiExists": {"value": "${SERVICE_NLP_API_RESOURCE_EXISTS=false}"}, "newrelicApiKey": {"value": "${NEWRELIC_API_KEY}"}}}