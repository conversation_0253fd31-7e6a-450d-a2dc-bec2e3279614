﻿using RealPlusNLP.Api.Abstractions.Services;
using RealPlusNLP.Api.Common.Enums;
using RealPlusNLP.Api.Common.Models;
using System.ComponentModel;

namespace RealPlusNLP.Api.Features.Listings.QueryToSearchOptions.Plugins;

public class NeighborhoodPlugin(INeighborhoodService neighborhoodService)
{
    private readonly INeighborhoodService neighborhoodService = neighborhoodService;

    [Description("""
        Returns the NYC neighborhood ids and names for given city area names and types.
        If you see something like Downtown, Downtown in Manhattan, Downtown in New York (NYC), then CityAreaType.Section has priority over CityAreaType.Neighborhood.
        For Downtown Brooklyn use CityAreaType.Neighborhood.
    """)]
    public NeighborhoodModel[] GetNeighborhoods(
        [Description("A list of city area names and their corresponding types")]
        List<CityArea> cityAreas)
    {
        return [.. cityAreas
            .SelectMany(d => neighborhoodService.Search(d.Name, d.Type))
            .Distinct()];
    }
}

[Description("NYC city areas and their types")]
public record CityArea(
    [property: Description("City area name")] string Name,
    [property: Description("City area type")] CityAreaType Type);
