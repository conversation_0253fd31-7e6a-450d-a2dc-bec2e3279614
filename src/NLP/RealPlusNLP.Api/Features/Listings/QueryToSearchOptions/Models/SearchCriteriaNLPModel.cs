using System.ComponentModel;
using System.Text.Json.Serialization;
using RealPlusNLP.Api.Common.Models;

namespace RealPlusNLP.Api.Features.Listings.QueryToSearchOptions.Models;

public sealed record SearchCriteriaNLPModel(
    ListingCategoryType ListingCategory,
    string? BuildingPeriods,
    string? OwnershipType,
    string? Amenities,
    string? AttendedLobby,
    [property: Description("If, and only if, user uses 'exact' or 'only' words before number of bedrooms, set this number to both min and max fields.")]
    [property: JsonPropertyName("bedrooms_min")] string? BedroomsMin,
    [property: JsonPropertyName("bedrooms_max")] string? BedroomsMax,
    [property: Description("If, and only if, user uses 'exact' or 'only' words before number of bathrooms, set this number to both min and max fields.")]
    [property: JsonPropertyName("bathrooms_min")] string? BathroomsMin,
    [property: JsonPropertyName("bathrooms_max")] string? BathroomsMax,
    [property: Description("If, and only if, user uses 'exact' or 'only' words before number of rooms, set this number to both min and max fields.")]
    [property: JsonPropertyName("rooms_min")] double? RoomsMin,
    [property: JsonPropertyName("rooms_max")] double? RoomsMax,
    [property: JsonPropertyName("price_min")] string? PriceMin,
    [property: JsonPropertyName("price_max")] string? PriceMax,
    [property: JsonPropertyName("sqft_min")]
    [property: Description("Min apartment square feet. Should be less than or equal to sqft_max")]
    double? SqFtMin,
    [property: JsonPropertyName("sqft_max")]
    [property: Description("Max apartment square feet. Should be more than or equal to sqft_min")]
    double? SqFtMax,
    [property: Description("a list of neighborhoods (id and name)")]
    NeighborhoodModel[]? Neighborhoods,
    [property: Description("User can use short names for statuses. e.g. CS for ContractSigned, LS for LeaseSigned, etc.")]
    ListingStatus[]? ListingStatus,
    [property: Description("List of the NYC building names and  their related RPBin numbers")]
    PropertyModel[]? Buildings,
    [property: JsonPropertyName("unprocessed_criteria")] string? UnprocessedCriteria);

[JsonConverter(typeof(JsonStringEnumConverter))]
[Description("Listing category types: Sales or Rentals. If nothing is specified set to Sales (8). If you see 'rental ownership' or 'ownership rental' set to Rentals")]
public enum ListingCategoryType
{
    Sales = 8,
    Rentals = 7
}

[JsonConverter(typeof(JsonStringEnumConverter))]
[Description("Status of the listing")]
public enum ListingStatus
{
    [Description("Status of the sale and rental 'active' listings")]
    Active,
    [Description("Status of the sale 'contract signed' listings")]
    ContractSigned,
    [Description("Status of the rental 'lease signed' listings")]
    LeaseSigned,
    [Description("Status of the sale 'sold' listings")]
    Sold,
    [Description("Status of the rental 'rented' listings")]
    Rented,
    [Description("Status of the sale and rental 'closed' listings")]
    Closed,
    [Description("Status of the sale and rental 'off market' listings")]
    OffMarket
}