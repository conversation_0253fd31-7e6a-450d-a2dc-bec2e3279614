﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.OpenApi.Models;
using RealPlusNLP.Api.Configuration.Options;
using Scalar.AspNetCore;

namespace RealPlusNLP.Api.Configuration.DependencyInjection;

public static class OpenApiConfig
{
    public static IServiceCollection AddOpenApiConfig(
        this IServiceCollection services)
    {
        // Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
        services.AddOpenApi(options =>
        {
            var jwtBearerSecurityScheme = new OpenApiSecurityScheme
            {
                Type = SecuritySchemeType.Http,
                Name = JwtBearerDefaults.AuthenticationScheme,
                Scheme = JwtBearerDefaults.AuthenticationScheme,
                Reference = new()
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = JwtBearerDefaults.AuthenticationScheme
                },
                Description = "JWT Bearer Token Authentication"
            };

            options.AddDocumentTransformer((document, context, cancellationToken) =>
            {
                document.Info = new()
                {
                    Title = "RealPlus NLP API",
                    Version = "v1",
                    Description = "API for query string to search options transformation."
                };
#if !DEBUG
                // Add Security Definition
                var apiKeySecurityScheme = new OpenApiSecurityScheme
                {
                    Type = SecuritySchemeType.ApiKey,
                    Name = AuthenticationOptions.ApiKeyHeaderName,
                    Scheme = "ApiKey",
                    In = ParameterLocation.Header,
                    Description = "API Key Authentication"
                };
                document.Components = new OpenApiComponents
                {
                    SecuritySchemes = new Dictionary<string, OpenApiSecurityScheme>
                    {
                        ["ApiKey"] = apiKeySecurityScheme,
                        [JwtBearerDefaults.AuthenticationScheme] = jwtBearerSecurityScheme
                    }
                };
                // Apply the security requirement to all operations
                //document.SecurityRequirements =
                //[
                //    new OpenApiSecurityRequirement
                //    {
                //        {
                //            new OpenApiSecurityScheme
                //            {
                //                Reference = new OpenApiReference
                //                {
                //                    Type = ReferenceType.SecurityScheme,
                //                    Id = "ApiKey"
                //                }
                //            }, []
                //        }
                //    },
                //    new OpenApiSecurityRequirement
                //    {
                //        {
                //            new OpenApiSecurityScheme
                //            {
                //                Reference = new OpenApiReference
                //                {
                //                    Type = ReferenceType.SecurityScheme,
                //                    Id = JwtBearerDefaults.AuthenticationScheme
                //                }
                //            }, []
                //        }
                //    }
                //];
#endif
                return Task.CompletedTask;
            });

            options.AddOperationTransformer((operation, context, cancellationToken) =>
            {
                if (context.Description.ActionDescriptor.EndpointMetadata.OfType<IAuthorizeData>().Any())
                {
                    operation.Security = [new() { [jwtBearerSecurityScheme] = [] }];
                }

                return Task.CompletedTask;
            });
        });

        return services;
    }

    public static void MapOpenApiConfig(this WebApplication app)
    {
        if (app.Environment.IsDevelopment())
        {
            app.MapOpenApi()
                .CacheOutput();
            app.MapScalarApiReference(options =>
            {
                options.Title = "RealPlus NLP API";
                //options.DefaultFonts = false;
#if !DEBUG
                options.Authentication = new ScalarAuthenticationOptions
                {
                    PreferredSecurityScheme = "ApiKey",
                };
#endif
            });
        }
    }
}
