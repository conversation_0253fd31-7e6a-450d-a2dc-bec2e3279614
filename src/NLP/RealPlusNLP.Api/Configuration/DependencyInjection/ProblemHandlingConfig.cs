﻿using Microsoft.AspNetCore.Http.Features;
using RealPlusNLP.Api.Common.Exceptions;
using System.Diagnostics;

namespace RealPlusNLP.Api.Configuration.DependencyInjection;

public static class ProblemHandlingConfig
{
    public static IServiceCollection AddProblemHandling(this IServiceCollection services)
    {
        services.AddProblemDetails(o =>
        {
            o.CustomizeProblemDetails = (ctx) =>
            {
                ctx.ProblemDetails.Instance =
                    $"{ctx.HttpContext.Request.Method} {ctx.HttpContext.Request.Path}";

                ctx.ProblemDetails
                    .Extensions
                    .TryAdd("traceId", ctx.HttpContext.TraceIdentifier);

                Activity? activity = ctx.HttpContext
                    .Features.Get<IHttpActivityFeature>()?
                    .Activity;
                ctx.ProblemDetails
                    .Extensions
                    .TryAdd("activityId", activity?.Id);
            };
        });

        services.AddExceptionHandler<CustomExceptionHandler>();

        return services;
    }

    public static void UseProblemHandling(this WebApplication app)
    {
        // Converts unhandled exceptions into Problem Details responses
        app.UseExceptionHandler(new ExceptionHandlerOptions
        {
            StatusCodeSelector = ex => ex switch
            {
                ArgumentException => StatusCodes.Status400BadRequest,
                KeyNotFoundException => StatusCodes.Status404NotFound,
                _ => StatusCodes.Status500InternalServerError
            }
        });

        // Returns the Problem Details response for (empty) non-successful responses
        app.UseStatusCodePages();
    }
}
