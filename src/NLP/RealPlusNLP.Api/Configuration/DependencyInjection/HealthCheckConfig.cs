﻿using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace RealPlusNLP.Api.Configuration.DependencyInjection;

public static class HealthCheckConfig
{
    public static IServiceCollection AddHealthCheck(
        this IServiceCollection services)
    {
        services.AddHealthChecks()
            .AddCheck("startup", () => HealthCheckResult.Healthy(), tags: ["startup"])
            .AddCheck("liveness", () => HealthCheckResult.Healthy(), tags: ["liveness"])
            .AddCheck("ready", () => HealthCheckResult.Healthy(), tags: ["ready"]);

        return services;
    }

    public static void MapHealthCheck(
        this WebApplication app)
    {
        app.MapHealthChecks("/health");

        app.MapHealthChecks("/health/startup", new HealthCheckOptions
        {
            Predicate = (check) => check.Tags.Contains("startup")
        });
        app.MapHealthChecks("/health/liveness", new HealthCheckOptions
        {
            Predicate = (check) => check.Tags.Contains("liveness")
        });
        app.MapHealthChecks("/health/ready", new HealthCheckOptions
        {
            Predicate = (check) => check.Tags.Contains("ready")
        });
    }
}
