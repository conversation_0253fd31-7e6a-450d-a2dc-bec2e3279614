﻿using RealPlusNLP.Api.Common.Behaviors;

namespace RealPlusNLP.Api.Configuration.DependencyInjection;

public static class MediatRConfig
{
    public static IServiceCollection AddMediatr(this IServiceCollection services)
    {
        var assembly = typeof(Program).Assembly;

        services.AddMediatR(options => {
            options.RegisterServicesFromAssembly(assembly);
            options.AddOpenBehavior(typeof(ValidationBehavior<,>));
        });

        //services.AddValidatorsFromAssembly(assembly);

        return services;
    }
}
