﻿namespace RealPlusNLP.Api.Configuration.DependencyInjection;

public static class ApiVersioningConfig
{
    public static IServiceCollection AddAppApiVersioning(
        this IServiceCollection services)
    {
        services.AddApiVersioning(options =>
        {
            options.AssumeDefaultVersionWhenUnspecified = true;
            options.ReportApiVersions = true;
            options.DefaultApiVersion = new ApiVersion(1, 0);
            options.ApiVersionReader = ApiVersionReader.Combine(
                //new UrlSegmentApiVersionReader(),
                new MediaTypeApiVersionReader(),
                new QueryStringApiVersionReader("v"), // may have space after v
                new HeaderApiVersionReader("api-version"));
        }).AddApiExplorer(options =>
        {
            options.GroupNameFormat = "'v'VVV";
            options.SubstituteApiVersionInUrl = false; // true;
        });

        return services;
    }

    public static void MapAppApiVersioning(
        this WebApplication app)
    {
        var versionSet = app.NewApiVersionSet()
            .HasApiVersion(new ApiVersion(1, 0))
            .ReportApiVersions()
            .Build();
        var routeBuilder = app.MapGroup("") //app.MapGroup("/v{version:apiVersion}")
            .WithApiVersionSet(versionSet)
            .MapToApiVersion(1, 0);
        
        routeBuilder.MapCarter();
    }
}