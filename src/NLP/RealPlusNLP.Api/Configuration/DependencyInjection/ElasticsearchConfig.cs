using Elasticsearch.Net;
using Microsoft.Extensions.Options;
using Nest;
using RealPlusNLP.Api.Abstractions.Services;
using RealPlusNLP.Api.Common.Documents;
using RealPlusNLP.Api.Configuration.Options;
using RealPlusNLP.Api.Infrastructure.Services;

namespace RealPlusNLP.Api.Configuration.DependencyInjection;

public static class ElasticsearchConfig
{
    public static IServiceCollection AddElasticsearchConfig(
        this IServiceCollection services,
        ConfigurationManager configuration)
    {
        services.AddOptions<ElasticsearchOptions>()
            .Bind(configuration.GetSection(nameof(ElasticsearchOptions)))
            .ValidateDataAnnotations()
            .ValidateOnStart();

        services.AddSingleton<IElasticClient>(options =>
        {
            var esOptions = options.GetRequiredService<IOptions<ElasticsearchOptions>>().Value;

            var pool = new SingleNodeConnectionPool(new Uri(esOptions.Url));
            var settings = new ConnectionSettings(pool)
                .BasicAuthentication(esOptions.Username, esOptions.Password)
                .EnableHttpCompression()
#if DEBUG
                .PrettyJson()
                .EnableDebugMode()
#endif
                .DefaultMappingFor<PropertyDocument>(m => m
                    .IdProperty(d => d.PropertyId));

            return new ElasticClient(settings);
        });

        services.AddScoped<IElasticsearchService, ElasticsearchService>();

        return services;
    }
}
