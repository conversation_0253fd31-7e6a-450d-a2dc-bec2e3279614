﻿using RealPlusNLP.Api.Configuration.Options;
using Microsoft.Extensions.Options;

namespace RealPlusNLP.Api.Common.Filters;

public sealed class ApiKeyEndpointFilter(IOptions<AuthenticationOptions> authOptions)
    : IEndpointFilter
{
    private readonly AuthenticationOptions _authOptions = authOptions.Value;

    public async ValueTask<object?> InvokeAsync(EndpointFilterInvocationContext context, EndpointFilterDelegate next)
    {
        if (!context.HttpContext.Request.Headers.TryGetValue(AuthenticationOptions.ApiKeyHeaderName, out var apiKeyValue) ||
            !_authOptions.ApiKey.Equals(apiKeyValue))
        {
            return TypedResults.Unauthorized();
        }

        return await next(context);
    }
}

