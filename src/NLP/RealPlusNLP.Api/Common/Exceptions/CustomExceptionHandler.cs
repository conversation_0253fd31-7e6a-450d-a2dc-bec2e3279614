using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Mvc;

namespace RealPlusNLP.Api.Common.Exceptions;

public sealed class CustomExceptionHandler(
    IProblemDetailsService problemDetailsService,
    ILogger<CustomExceptionHandler> logger,
    IHostEnvironment environment)
    : IExceptionHandler
{
    public async ValueTask<bool> TryHandleAsync(
        HttpContext httpContext,
        Exception exception,
        CancellationToken cancellationToken)
    {
        logger.LogError(exception, "Error occurred: {ExceptionType} {ExceptionMessage}\n{StackTrace}",
            exception.GetType().Name, exception.Message, exception.StackTrace);

        var problemDetails = new ProblemDetails
        {
            Status = exception switch
            {
                ArgumentException => StatusCodes.Status400BadRequest,
                ValidationException => StatusCodes.Status400BadRequest,
                _ => StatusCodes.Status500InternalServerError
            },
            Title = "An error occurred",
            Type = exception.GetType().Name,
            Detail = exception.Message,
            Instance = httpContext.Request.Path
        };

        if (exception is ValidationException validationException)
        {
            problemDetails.Extensions.Add("ValidationExceptions", validationException.Errors);
        }
        if (exception.InnerException != null)
        {
            problemDetails.Extensions.Add("innerExceptionMessage", exception.InnerException.Message);
            problemDetails.Extensions.Add("innerExceptionType", exception.InnerException.GetType().Name);
        }
        if (!environment.IsProduction())
        {
            problemDetails.Extensions.Add("stackTrace", exception.StackTrace);
            if (exception.InnerException != null)
            {
                problemDetails.Extensions.Add("innerExceptionStackTrace", exception.InnerException.StackTrace);
            }
        }

        return await problemDetailsService.TryWriteAsync(new ProblemDetailsContext
        {
            Exception = exception,
            HttpContext = httpContext,
            ProblemDetails = problemDetails
        });
    }
}
