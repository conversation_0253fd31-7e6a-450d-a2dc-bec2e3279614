<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<UserSecretsId>fcf9de8c-c450-42b3-8722-22bc66c646e8</UserSecretsId>
		<Platforms>AnyCPU;x64</Platforms>
	</PropertyGroup>

	<ItemGroup>
		<Compile Remove="Common\Extensions\**" />
		<Content Remove="Common\Extensions\**" />
		<EmbeddedResource Remove="Common\Extensions\**" />
		<None Remove="Common\Extensions\**" />
	</ItemGroup>

	<ItemGroup>
		<Content Include="Features\Listings\QueryToSearchOptions\prompt.md">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Azure.AI.OpenAI" Version="2.1.0" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.7" />
		<PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.7" />
		<PackageReference Include="Microsoft.Extensions.AI" Version="9.7.1" />
		<PackageReference Include="Microsoft.Extensions.AI.OpenAI" Version="9.5.0-preview.1.25262.9" />
		<PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.7" />
		<PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.7" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.22.1" />
		<PackageReference Include="Mapster" Version="7.4.0" />
		<PackageReference Include="Mapster.DependencyInjection" Version="1.0.1" />
		<PackageReference Include="Asp.Versioning.http" Version="8.1.0" />
		<PackageReference Include="Asp.Versioning.Mvc.ApiExplorer" Version="8.1.0" />
		<PackageReference Include="Carter" Version="9.0.0" />
		<PackageReference Include="FluentValidation" Version="12.0.0" />
		<!--<PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />-->
		<PackageReference Include="MediatR" Version="13.0.0" />
		<PackageReference Include="Scalar.AspNetCore" Version="2.6.4" />
		<PackageReference Include="System.Text.Json" Version="9.0.7" />
		<PackageReference Include="Azure.Identity" Version="1.14.2" />
		<PackageReference Include="OpenTelemetry.Exporter.Console" Version="1.12.0" />
		<PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.12.0" />
		<PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.12.0" />
		<PackageReference Include="OpenTelemetry.Instrumentation.Http" Version="1.12.0" />
		<PackageReference Include="OpenTelemetry.Instrumentation.Runtime" Version="1.12.0" />
		<PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.12.0" />
		<PackageReference Include="Azure.Monitor.OpenTelemetry.AspNetCore" Version="1.3.0" />
		<PackageReference Include="NJsonSchema" Version="11.3.2" />
		<PackageReference Include="NEST" Version="7.17.5" />
	</ItemGroup>

</Project>
