{"profiles": {"http": {"commandName": "Project", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "applicationUrl": "http://localhost:5042"}, "https": {"commandName": "Project", "launchBrowser": true, "launchUrl": "scalar/v1", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "AZURE_OPENAI_ENDPOINT": "https://ai-foundry-dev.openai.azure.com/", "APPLICATIONINSIGHTS_CONNECTION_STRING": "InstrumentationKey=78b67d29-bc16-454e-a054-181ea4091b8b;IngestionEndpoint=https://eastus-8.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/;ApplicationId=9dd07a7e-3490-40f1-a034-83a49b50ccfc", "NEWRELIC_API_KEY": "190f353cad40b077659390cd1d837d0fFFFFNRAL"}, "dotnetRunMessages": true, "applicationUrl": "https://localhost:7014;http://localhost:5042"}, "Container (Dockerfile)": {"commandName": "<PERSON>er", "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}", "environmentVariables": {"ASPNETCORE_HTTPS_PORTS": "8081", "ASPNETCORE_HTTP_PORTS": "8080"}, "publishAllPorts": true, "useSSL": true}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}, "$schema": "https://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:54926/", "sslPort": 44344}}}