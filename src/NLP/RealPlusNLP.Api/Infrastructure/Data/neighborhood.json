[{"id": 3167, "parentId": 3163, "name": "Bronx", "source": "Borough"}, {"id": 3165, "parentId": 3163, "name": "Brooklyn", "source": "Borough"}, {"id": 3164, "parentId": 3163, "name": "Manhattan", "source": "Borough"}, {"id": 3166, "parentId": 3163, "name": "Queens", "source": "Borough"}, {"id": 3168, "parentId": 3163, "name": "Staten Island", "source": "Borough"}, {"id": 3170, "parentId": 3164, "name": "All Midtown", "source": "Section"}, {"id": 3891, "parentId": 3165, "name": "Bay Ridge", "source": "Section"}, {"id": 3846, "parentId": 3165, "name": "Bedford - Stuyvesant", "source": "Section"}, {"id": 3892, "parentId": 3165, "name": "Borough Park", "source": "Section"}, {"id": 3882, "parentId": 3172, "name": "Central Harlem", "source": "Section"}, {"id": 3893, "parentId": 3165, "name": "Crown Heights", "source": "Section"}, {"id": 3895, "parentId": 3165, "name": "Ditmas Park", "source": "Section"}, {"id": 3169, "parentId": 3164, "name": "Downtown", "source": "Section"}, {"id": 3894, "parentId": 3165, "name": "DUMBO", "source": "Section"}, {"id": 3896, "parentId": 3165, "name": "East Flatbush", "source": "Section"}, {"id": 3897, "parentId": 3165, "name": "East New York", "source": "Section"}, {"id": 3886, "parentId": 3169, "name": "Financial District", "source": "Section"}, {"id": 3887, "parentId": 3169, "name": "Flatiron", "source": "Section"}, {"id": 3888, "parentId": 3169, "name": "Greenwich Village", "source": "Section"}, {"id": 3889, "parentId": 3169, "name": "Lower East Side", "source": "Section"}, {"id": 3901, "parentId": 3167, "name": "Riverdale", "source": "Section"}, {"id": 3174, "parentId": 3164, "name": "Roosevelt Island", "source": "Section"}, {"id": 3898, "parentId": 3165, "name": "Sheepshead Bay", "source": "Section"}, {"id": 3890, "parentId": 3169, "name": "Soho", "source": "Section"}, {"id": 3737, "parentId": 3780, "name": "Suffolk County", "source": "Section"}, {"id": 3171, "parentId": 3164, "name": "Upper East Side (UES)", "source": "Section"}, {"id": 3172, "parentId": 3164, "name": "Upper Manhattan", "source": "Section"}, {"id": 3173, "parentId": 3164, "name": "Upper West Side (UWS)", "source": "Section"}, {"id": 3883, "parentId": 3172, "name": "Washington Heights", "source": "Section"}, {"id": 3884, "parentId": 3172, "name": "West Harlem", "source": "Section"}, {"id": 102, "parentId": 3165, "name": "Williamsburg", "source": "Section"}, {"id": 3197, "parentId": 3170, "name": "All Midtown East", "source": "Neighborhood"}, {"id": 3202, "parentId": 3170, "name": "All Midtown West", "source": "Neighborhood"}, {"id": 3621, "parentId": 3167, "name": "Allerton", "source": "Neighborhood"}, {"id": 3666, "parentId": 3166, "name": "Alley Pond Park", "source": "Neighborhood"}, {"id": 3738, "parentId": 3737, "name": "Amagansett", "source": "Neighborhood"}, {"id": 3487, "parentId": 3168, "name": "Annadale", "source": "neighborhood"}, {"id": 3488, "parentId": 3168, "name": "Arden Heights", "source": "neighborhood"}, {"id": 3458, "parentId": 3168, "name": "Arlington", "source": "neighborhood"}, {"id": 3422, "parentId": 3168, "name": "Arrochar", "source": "neighborhood"}, {"id": 3411, "parentId": 3166, "name": "<PERSON><PERSON><PERSON>", "source": "Neighborhood"}, {"id": 3342, "parentId": 3166, "name": "Astoria", "source": "Neighborhood"}, {"id": 3767, "parentId": 3737, "name": "Babylon", "source": "Neighborhood"}, {"id": 3277, "parentId": 3165, "name": "Bath Beach", "source": "Neighborhood"}, {"id": 3175, "parentId": 3169, "name": "Battery Park City", "source": "Neighborhood"}, {"id": 3278, "parentId": 3891, "name": "Bay Ridge", "source": "Neighborhood"}, {"id": 3423, "parentId": 3166, "name": "Bay Terrace", "source": "Neighborhood"}, {"id": 3658, "parentId": 3168, "name": "Bay Terrace 1", "source": "Neighborhood"}, {"id": 3226, "parentId": 3167, "name": "Baychester", "source": "Neighborhood"}, {"id": 3344, "parentId": 3166, "name": "Bayside", "source": "Neighborhood"}, {"id": 3412, "parentId": 3166, "name": "Bayswater", "source": "Neighborhood"}, {"id": 3279, "parentId": 3846, "name": "Bedford - Stuyvesant", "source": "Neighborhood"}, {"id": 3227, "parentId": 3167, "name": "Bedford Park", "source": "Neighborhood"}, {"id": 3204, "parentId": 3197, "name": "<PERSON><PERSON>", "source": "Neighborhood"}, {"id": 3413, "parentId": 3166, "name": "Belle Harbor", "source": "Neighborhood"}, {"id": 3345, "parentId": 3166, "name": "<PERSON><PERSON>", "source": "Neighborhood"}, {"id": 3228, "parentId": 3167, "name": "Belmont", "source": "Neighborhood"}, {"id": 3280, "parentId": 3165, "name": "Bensonhurst", "source": "Neighborhood"}, {"id": 3281, "parentId": 3165, "name": "Bergen Beach", "source": "Neighborhood"}, {"id": 3507, "parentId": 3168, "name": "Bloomfield", "source": "neighborhood"}, {"id": 3282, "parentId": 3165, "name": "Boerum Hill", "source": "Neighborhood"}, {"id": 3283, "parentId": 3892, "name": "Borough Park", "source": "Neighborhood"}, {"id": 3414, "parentId": 3166, "name": "Breezy Point", "source": "Neighborhood"}, {"id": 3768, "parentId": 3737, "name": "Brentwood", "source": "Neighborhood"}, {"id": 3347, "parentId": 3166, "name": "<PERSON><PERSON><PERSON><PERSON>", "source": "Neighborhood"}, {"id": 3739, "parentId": 3737, "name": "Bridgehampton", "source": "Neighborhood"}, {"id": 3284, "parentId": 3165, "name": "Brighton Beach", "source": "Neighborhood"}, {"id": 3459, "parentId": 3168, "name": "Brighton Heights", "source": "neighborhood"}, {"id": 3676, "parentId": 3166, "name": "Broad Channel", "source": "Neighborhood"}, {"id": 3259, "parentId": 3167, "name": "Bronxdale", "source": "Neighborhood"}, {"id": 3229, "parentId": 3167, "name": "Bronxwood", "source": "Neighborhood"}, {"id": 3769, "parentId": 3737, "name": "Brookhaven", "source": "Neighborhood"}, {"id": 3285, "parentId": 3165, "name": "Brooklyn Heights", "source": "Neighborhood"}, {"id": 3286, "parentId": 3165, "name": "Brownsville", "source": "Neighborhood"}, {"id": 3659, "parentId": 3168, "name": "Bull's Head", "source": "Neighborhood"}, {"id": 3445, "parentId": 3168, "name": "Bulls Head", "source": "neighborhood"}, {"id": 3287, "parentId": 3165, "name": "Bushwick", "source": "Neighborhood"}, {"id": 3489, "parentId": 3168, "name": "Butler Manor", "source": "neighborhood"}, {"id": 3740, "parentId": 3737, "name": "<PERSON><PERSON>", "source": "Neighborhood"}, {"id": 3349, "parentId": 3166, "name": "Cambria Heights", "source": "Neighborhood"}, {"id": 3288, "parentId": 3165, "name": "Canarsie", "source": "Neighborhood"}, {"id": 3209, "parentId": 3171, "name": "Carnegie Hill", "source": "Neighborhood"}, {"id": 3289, "parentId": 3165, "name": "Carroll Gardens", "source": "Neighborhood"}, {"id": 3230, "parentId": 3167, "name": "Castle Hill", "source": "Neighborhood"}, {"id": 3446, "parentId": 3168, "name": "Castleton Corners", "source": "neighborhood"}, {"id": 3214, "parentId": 3882, "name": "Central Harlem", "source": "Neighborhood"}, {"id": 3844, "parentId": 3202, "name": "Central Park South", "source": "Neighborhood"}, {"id": 3260, "parentId": 3901, "name": "Central Riverdale", "source": "Neighborhood"}, {"id": 3490, "parentId": 3168, "name": "Charleston", "source": "neighborhood"}, {"id": 3176, "parentId": 3169, "name": "Chelsea", "source": "Neighborhood"}, {"id": 3508, "parentId": 3168, "name": "Chelsea (Staten Island)", "source": "neighborhood"}, {"id": 3177, "parentId": 3169, "name": "Chinatown", "source": "Neighborhood"}, {"id": 3623, "parentId": 3167, "name": "City Island", "source": "Neighborhood"}, {"id": 3332, "parentId": 3897, "name": "City Line", "source": "Neighborhood"}, {"id": 3251, "parentId": 3167, "name": "Claremont", "source": "Neighborhood"}, {"id": 3624, "parentId": 3167, "name": "Claremont Village", "source": "Neighborhood"}, {"id": 3625, "parentId": 3167, "name": "Clason Point", "source": "Neighborhood"}, {"id": 3460, "parentId": 3168, "name": "Clifton", "source": "neighborhood"}, {"id": 3290, "parentId": 3165, "name": "<PERSON>", "source": "Neighborhood"}, {"id": 3447, "parentId": 3168, "name": "Clove Lakes", "source": "neighborhood"}, {"id": 3232, "parentId": 3167, "name": "Co-op City", "source": "Neighborhood"}, {"id": 3291, "parentId": 3165, "name": "Cobble Hill", "source": "Neighborhood"}, {"id": 3351, "parentId": 3166, "name": "College Point", "source": "Neighborhood"}, {"id": 3292, "parentId": 3165, "name": "Columbia St Waterfront District", "source": "Neighborhood"}, {"id": 3424, "parentId": 3168, "name": "Concord", "source": "neighborhood"}, {"id": 3233, "parentId": 3167, "name": "Concourse", "source": "Neighborhood"}, {"id": 3626, "parentId": 3167, "name": "Concourse Village", "source": "Neighborhood"}, {"id": 3293, "parentId": 3165, "name": "Coney Island", "source": "Neighborhood"}, {"id": 3352, "parentId": 3166, "name": "Corona", "source": "Neighborhood"}, {"id": 3234, "parentId": 3167, "name": "Country Club", "source": "Neighborhood"}, {"id": 3235, "parentId": 3167, "name": "Crotona Park East", "source": "Neighborhood"}, {"id": 3294, "parentId": 3893, "name": "Crown Heights", "source": "Neighborhood"}, {"id": 3677, "parentId": 3166, "name": "Cunningham Park", "source": "Neighborhood"}, {"id": 3741, "parentId": 3737, "name": "<PERSON><PERSON><PERSON>", "source": "Neighborhood"}, {"id": 3770, "parentId": 3737, "name": "Deer Park", "source": "Neighborhood"}, {"id": 3678, "parentId": 3166, "name": "<PERSON><PERSON><PERSON><PERSON>", "source": "Neighborhood"}, {"id": 3297, "parentId": 3895, "name": "Ditmas Park", "source": "Neighborhood"}, {"id": 3425, "parentId": 3168, "name": "Dongan Hills", "source": "neighborhood"}, {"id": 3354, "parentId": 3166, "name": "<PERSON><PERSON>", "source": "Neighborhood"}, {"id": 3298, "parentId": 3165, "name": "Downtown Brooklyn", "source": "Neighborhood"}, {"id": 3296, "parentId": 3894, "name": "DUMBO", "source": "Neighborhood"}, {"id": 3299, "parentId": 3165, "name": "Dyker Heights", "source": "Neighborhood"}, {"id": 3355, "parentId": 3166, "name": "East Elmhurst", "source": "Neighborhood"}, {"id": 3606, "parentId": 3896, "name": "East Flatbush", "source": "Neighborhood"}, {"id": 3742, "parentId": 3737, "name": "East Hampton", "source": "Neighborhood"}, {"id": 3215, "parentId": 3172, "name": "East Harlem", "source": "Neighborhood"}, {"id": 3743, "parentId": 3737, "name": "East Moriches", "source": "Neighborhood"}, {"id": 3628, "parentId": 3167, "name": "East Morrisania", "source": "Neighborhood"}, {"id": 3300, "parentId": 3897, "name": "East New York", "source": "Neighborhood"}, {"id": 3744, "parentId": 3737, "name": "East Quogue", "source": "Neighborhood"}, {"id": 3421, "parentId": 3168, "name": "East Shore", "source": "neighborhood"}, {"id": 3236, "parentId": 3167, "name": "East Tremont", "source": "Neighborhood"}, {"id": 3179, "parentId": 3169, "name": "East Village", "source": "Neighborhood"}, {"id": 3327, "parentId": 102, "name": "East Williamsburg", "source": "Neighborhood"}, {"id": 3238, "parentId": 3167, "name": "Eastchester", "source": "Neighborhood"}, {"id": 3239, "parentId": 3167, "name": "Edenwald", "source": "Neighborhood"}, {"id": 3416, "parentId": 3166, "name": "Edgemere", "source": "Neighborhood"}, {"id": 3426, "parentId": 3168, "name": "Egbertville", "source": "neighborhood"}, {"id": 3461, "parentId": 3168, "name": "Elm Park", "source": "neighborhood"}, {"id": 3356, "parentId": 3166, "name": "Elmhurst", "source": "Neighborhood"}, {"id": 3491, "parentId": 3168, "name": "Eltingville", "source": "neighborhood"}, {"id": 3427, "parentId": 3168, "name": "<PERSON>", "source": "neighborhood"}, {"id": 3785, "parentId": 3901, "name": "Estate Area", "source": "Neighborhood"}, {"id": 3417, "parentId": 3166, "name": "Far Rockaway", "source": "Neighborhood"}, {"id": 3339, "parentId": 3896, "name": "Farragut", "source": "Neighborhood"}, {"id": 3261, "parentId": 3901, "name": "<PERSON><PERSON>", "source": "Neighborhood"}, {"id": 3180, "parentId": 3886, "name": "Financial District", "source": "Neighborhood"}, {"id": 3839, "parentId": 3895, "name": "Fiske Terrace", "source": "Neighborhood"}, {"id": 3301, "parentId": 3165, "name": "Flatbush", "source": "Neighborhood"}, {"id": 3181, "parentId": 3887, "name": "Flatiron", "source": "Neighborhood"}, {"id": 3302, "parentId": 3165, "name": "Flatlands", "source": "Neighborhood"}, {"id": 3357, "parentId": 3166, "name": "Floral Park", "source": "Neighborhood"}, {"id": 3358, "parentId": 3166, "name": "Flushing", "source": "Neighborhood"}, {"id": 3240, "parentId": 3167, "name": "Fordham", "source": "Neighborhood"}, {"id": 3359, "parentId": 3166, "name": "Forest Hills", "source": "Neighborhood"}, {"id": 3680, "parentId": 3166, "name": "Forest Park", "source": "Neighborhood"}, {"id": 3223, "parentId": 3883, "name": "Fort George", "source": "Neighborhood"}, {"id": 3303, "parentId": 3165, "name": "Fort Greene", "source": "Neighborhood"}, {"id": 3334, "parentId": 3891, "name": "Fort Hamilton", "source": "Neighborhood"}, {"id": 3428, "parentId": 3168, "name": "Fort Wadsworth", "source": "neighborhood"}, {"id": 3462, "parentId": 3168, "name": "Fox Hills", "source": "neighborhood"}, {"id": 3360, "parentId": 3166, "name": "Fresh Meadows", "source": "Neighborhood"}, {"id": 3660, "parentId": 3168, "name": "Freshkills Park", "source": "Neighborhood"}, {"id": 3191, "parentId": 3886, "name": "Fulton/Seaport", "source": "Neighborhood"}, {"id": 3304, "parentId": 3165, "name": "Gerritsen Beach", "source": "Neighborhood"}, {"id": 3361, "parentId": 3166, "name": "Glen Oaks", "source": "Neighborhood"}, {"id": 3362, "parentId": 3166, "name": "Glendale", "source": "Neighborhood"}, {"id": 3305, "parentId": 3165, "name": "<PERSON><PERSON><PERSON>", "source": "Neighborhood"}, {"id": 3182, "parentId": 3169, "name": "Gramercy Park", "source": "Neighborhood"}, {"id": 3448, "parentId": 3168, "name": "Graniteville", "source": "neighborhood"}, {"id": 3429, "parentId": 3168, "name": "Grant City", "source": "neighborhood"}, {"id": 3430, "parentId": 3168, "name": "Grasmere", "source": "neighborhood"}, {"id": 3306, "parentId": 3165, "name": "Gravesend", "source": "Neighborhood"}, {"id": 3492, "parentId": 3168, "name": "Great Kills", "source": "neighborhood"}, {"id": 3307, "parentId": 3165, "name": "Greenpoint", "source": "Neighborhood"}, {"id": 3493, "parentId": 3168, "name": "<PERSON><PERSON>", "source": "neighborhood"}, {"id": 3183, "parentId": 3888, "name": "Greenwich Village (GV)", "source": "Neighborhood"}, {"id": 3308, "parentId": 3165, "name": "Greenwood Heights", "source": "Neighborhood"}, {"id": 3463, "parentId": 3168, "name": "<PERSON><PERSON><PERSON> Hill", "source": "neighborhood"}, {"id": 3216, "parentId": 3172, "name": "Hamilton Heights", "source": "Neighborhood"}, {"id": 3745, "parentId": 3737, "name": "Hampton Bays", "source": "Neighborhood"}, {"id": 3449, "parentId": 3168, "name": "Heartland Village", "source": "neighborhood"}, {"id": 3608, "parentId": 3202, "name": "Hell's Kitchen", "source": "Neighborhood"}, {"id": 3431, "parentId": 3168, "name": "High Rock", "source": "neighborhood"}, {"id": 3241, "parentId": 3167, "name": "Highbridge", "source": "Neighborhood"}, {"id": 3662, "parentId": 3168, "name": "Hoffman Island", "source": "Neighborhood"}, {"id": 3364, "parentId": 3166, "name": "<PERSON><PERSON>", "source": "Neighborhood"}, {"id": 3681, "parentId": 3166, "name": "Hollis Hills", "source": "Neighborhood"}, {"id": 3682, "parentId": 3166, "name": "<PERSON><PERSON><PERSON>", "source": "Neighborhood"}, {"id": 3337, "parentId": 3898, "name": "Homecrest", "source": "Neighborhood"}, {"id": 3683, "parentId": 3166, "name": "<PERSON>", "source": "Neighborhood"}, {"id": 3464, "parentId": 3168, "name": "Howland Hook", "source": "neighborhood"}, {"id": 3221, "parentId": 3883, "name": "Hudson Heights", "source": "Neighborhood"}, {"id": 3849, "parentId": 3890, "name": "Hudson Square", "source": "Neighborhood"}, {"id": 3609, "parentId": 3202, "name": "Hudson Yards", "source": "Neighborhood"}, {"id": 3494, "parentId": 3168, "name": "<PERSON><PERSON><PERSON>", "source": "neighborhood"}, {"id": 3495, "parentId": 3168, "name": "Huguenot Beach", "source": "neighborhood"}, {"id": 3771, "parentId": 3737, "name": "Huntington", "source": "Neighborhood"}, {"id": 3242, "parentId": 3167, "name": "Hunts Point", "source": "Neighborhood"}, {"id": 3217, "parentId": 3172, "name": "Inwood", "source": "Neighborhood"}, {"id": 3772, "parentId": 3737, "name": "<PERSON><PERSON>", "source": "Neighborhood"}, {"id": 3366, "parentId": 3166, "name": "Jackson Heights", "source": "Neighborhood"}, {"id": 3367, "parentId": 3166, "name": "Jamaica", "source": "Neighborhood"}, {"id": 3368, "parentId": 3166, "name": "Jamaica Estates", "source": "Neighborhood"}, {"id": 3369, "parentId": 3166, "name": "Jamaica Hills", "source": "Neighborhood"}, {"id": 3746, "parentId": 3737, "name": "Jamesport", "source": "Neighborhood"}, {"id": 3684, "parentId": 3166, "name": "<PERSON> International Airport", "source": "Neighborhood"}, {"id": 3309, "parentId": 3165, "name": "Kensington", "source": "Neighborhood"}, {"id": 3370, "parentId": 3166, "name": "Kew Gardens", "source": "Neighborhood"}, {"id": 3371, "parentId": 3166, "name": "Kew Gardens Hills", "source": "Neighborhood"}, {"id": 3243, "parentId": 3167, "name": "Kingsbridge", "source": "Neighborhood"}, {"id": 3244, "parentId": 3167, "name": "Kingsbridge Heights", "source": "Neighborhood"}, {"id": 3203, "parentId": 3169, "name": "Kips Bay", "source": "Neighborhood"}, {"id": 3450, "parentId": 3168, "name": "La Tourette", "source": "neighborhood"}, {"id": 3245, "parentId": 3167, "name": "Laconia", "source": "Neighborhood"}, {"id": 3685, "parentId": 3166, "name": "LaGuardia Airport", "source": "Neighborhood"}, {"id": 3663, "parentId": 3168, "name": "Latourette Park", "source": "Neighborhood"}, {"id": 3372, "parentId": 3166, "name": "<PERSON><PERSON>", "source": "Neighborhood"}, {"id": 3210, "parentId": 3171, "name": "Lenox Hill", "source": "Neighborhood"}, {"id": 3432, "parentId": 3168, "name": "Lighthouse Hill", "source": "neighborhood"}, {"id": 3205, "parentId": 3173, "name": "Lincoln Square", "source": "Neighborhood"}, {"id": 3773, "parentId": 3737, "name": "Lindenhurst", "source": "Neighborhood"}, {"id": 3184, "parentId": 3169, "name": "Little Italy", "source": "Neighborhood"}, {"id": 3373, "parentId": 3166, "name": "Little Neck", "source": "Neighborhood"}, {"id": 3465, "parentId": 3168, "name": "Livingston", "source": "neighborhood"}, {"id": 3774, "parentId": 3737, "name": "Lloyd Harbor", "source": "Neighborhood"}, {"id": 3266, "parentId": 3167, "name": "Locust Point", "source": "Neighborhood"}, {"id": 3246, "parentId": 3167, "name": "<PERSON><PERSON>", "source": "Neighborhood"}, {"id": 3185, "parentId": 3889, "name": "Lower East Side", "source": "Neighborhood"}, {"id": 3433, "parentId": 3168, "name": "Lower Todt Hill", "source": "neighborhood"}, {"id": 3851, "parentId": 3898, "name": "Madison", "source": "Neighborhood"}, {"id": 3310, "parentId": 3165, "name": "Manhattan Beach", "source": "Neighborhood"}, {"id": 3206, "parentId": 3173, "name": "Manhattan Valley", "source": "Neighborhood"}, {"id": 3218, "parentId": 3884, "name": "Manhattanville", "source": "Neighborhood"}, {"id": 3466, "parentId": 3168, "name": "Manor Heights", "source": "neighborhood"}, {"id": 3747, "parentId": 3737, "name": "Manorville", "source": "Neighborhood"}, {"id": 3335, "parentId": 3892, "name": "<PERSON>ton", "source": "Neighborhood"}, {"id": 3219, "parentId": 3172, "name": "Marble Hill", "source": "Neighborhood"}, {"id": 3311, "parentId": 3165, "name": "Marine Park", "source": "Neighborhood"}, {"id": 3467, "parentId": 3168, "name": "Mariners Harbor", "source": "neighborhood"}, {"id": 3375, "parentId": 3166, "name": "<PERSON><PERSON><PERSON>", "source": "Neighborhood"}, {"id": 3775, "parentId": 3737, "name": "<PERSON><PERSON>", "source": "Neighborhood"}, {"id": 3748, "parentId": 3737, "name": "Mattituck", "source": "Neighborhood"}, {"id": 3603, "parentId": 3169, "name": "Meat Packing", "source": "Neighborhood"}, {"id": 3451, "parentId": 3168, "name": "<PERSON><PERSON>", "source": "neighborhood"}, {"id": 3247, "parentId": 3167, "name": "<PERSON><PERSON>", "source": "Neighborhood"}, {"id": 3444, "parentId": 3168, "name": "Mid-Island", "source": "neighborhood"}, {"id": 3376, "parentId": 3166, "name": "Middle Village", "source": "Neighborhood"}, {"id": 3434, "parentId": 3168, "name": "Midland Beach", "source": "neighborhood"}, {"id": 3196, "parentId": 3202, "name": "Midtown", "source": "Neighborhood"}, {"id": 3201, "parentId": 3197, "name": "Midtown East", "source": "Neighborhood"}, {"id": 3312, "parentId": 3165, "name": "Midwood", "source": "Neighborhood"}, {"id": 3313, "parentId": 3165, "name": "Mill Basin", "source": "Neighborhood"}, {"id": 3749, "parentId": 3737, "name": "<PERSON>au<PERSON>", "source": "Neighborhood"}, {"id": 3207, "parentId": 3172, "name": "Morningside Heights", "source": "Neighborhood"}, {"id": 3248, "parentId": 3167, "name": "Morris Heights", "source": "Neighborhood"}, {"id": 3249, "parentId": 3167, "name": "Morris Park", "source": "Neighborhood"}, {"id": 3250, "parentId": 3167, "name": "Morrisania", "source": "Neighborhood"}, {"id": 3252, "parentId": 3167, "name": "Mott Haven", "source": "Neighborhood"}, {"id": 3631, "parentId": 3167, "name": "Mount Eden", "source": "Neighborhood"}, {"id": 3632, "parentId": 3167, "name": "Mount Hope", "source": "Neighborhood"}, {"id": 3496, "parentId": 3168, "name": "Mount Loretto", "source": "neighborhood"}, {"id": 3268, "parentId": 3167, "name": "Mt. Hope", "source": "Neighborhood"}, {"id": 3198, "parentId": 3197, "name": "<PERSON> Hill", "source": "Neighborhood"}, {"id": 3716, "parentId": 3165, "name": "Navy Yard", "source": "Neighborhood"}, {"id": 3419, "parentId": 3166, "name": "Neponsit", "source": "Neighborhood"}, {"id": 3468, "parentId": 3168, "name": "New Brighton", "source": "neighborhood"}, {"id": 3435, "parentId": 3168, "name": "New Dorp", "source": "neighborhood"}, {"id": 3436, "parentId": 3168, "name": "New Dorp Beach", "source": "neighborhood"}, {"id": 3333, "parentId": 3897, "name": "New Lots", "source": "Neighborhood"}, {"id": 3452, "parentId": 3168, "name": "New Springville", "source": "neighborhood"}, {"id": 3750, "parentId": 3737, "name": "New Suffolk", "source": "Neighborhood"}, {"id": 3194, "parentId": 3888, "name": "Noho", "source": "Neighborhood"}, {"id": 3186, "parentId": 3169, "name": "<PERSON><PERSON><PERSON>", "source": "Neighborhood"}, {"id": 3225, "parentId": 3887, "name": "NoMad", "source": "Neighborhood"}, {"id": 3751, "parentId": 3737, "name": "North Haven", "source": "Neighborhood"}, {"id": 3253, "parentId": 3167, "name": "North New York", "source": "Neighborhood"}, {"id": 3634, "parentId": 3901, "name": "North Riverdale", "source": "Neighborhood"}, {"id": 3457, "parentId": 3168, "name": "North Shore", "source": "neighborhood"}, {"id": 3254, "parentId": 3167, "name": "Norwood", "source": "Neighborhood"}, {"id": 3752, "parentId": 3737, "name": "Noyack", "source": "Neighborhood"}, {"id": 3437, "parentId": 3168, "name": "Oakwood", "source": "neighborhood"}, {"id": 3438, "parentId": 3168, "name": "Oakwood Beach", "source": "neighborhood"}, {"id": 3776, "parentId": 3737, "name": "Ocean Beach", "source": "Neighborhood"}, {"id": 3439, "parentId": 3168, "name": "Ocean Breeze", "source": "neighborhood"}, {"id": 3330, "parentId": 3846, "name": "Ocean Hill", "source": "Neighborhood"}, {"id": 3316, "parentId": 3165, "name": "Old Mill Basin", "source": "Neighborhood"}, {"id": 3497, "parentId": 3168, "name": "Old Place", "source": "neighborhood"}, {"id": 3440, "parentId": 3168, "name": "Old Town", "source": "neighborhood"}, {"id": 3635, "parentId": 3167, "name": "Olinville", "source": "Neighborhood"}, {"id": 3380, "parentId": 3166, "name": "Ozone Park", "source": "Neighborhood"}, {"id": 3469, "parentId": 3168, "name": "Park Hill", "source": "neighborhood"}, {"id": 3317, "parentId": 3165, "name": "Park Slope", "source": "Neighborhood"}, {"id": 3255, "parentId": 3167, "name": "Parkchester", "source": "Neighborhood"}, {"id": 3753, "parentId": 3737, "name": "Peconic", "source": "Neighborhood"}, {"id": 3256, "parentId": 3167, "name": "Pelham Bay", "source": "Neighborhood"}, {"id": 3257, "parentId": 3167, "name": "Pelham Gardens", "source": "Neighborhood"}, {"id": 3258, "parentId": 3167, "name": "Pelham Parkway", "source": "Neighborhood"}, {"id": 3498, "parentId": 3168, "name": "Pleasant Plains", "source": "neighborhood"}, {"id": 3470, "parentId": 3168, "name": "Port Ivory", "source": "neighborhood"}, {"id": 3653, "parentId": 3167, "name": "Port Morris", "source": "Neighborhood"}, {"id": 3471, "parentId": 3168, "name": "Port Richmond", "source": "neighborhood"}, {"id": 3664, "parentId": 3168, "name": "Prince's Bay", "source": "Neighborhood"}, {"id": 3499, "parentId": 3168, "name": "Princes Bay", "source": "neighborhood"}, {"id": 3318, "parentId": 3165, "name": "Prospect Heights", "source": "Neighborhood"}, {"id": 3319, "parentId": 3165, "name": "Prospect Lefferts Gardens", "source": "Neighborhood"}, {"id": 3320, "parentId": 3165, "name": "Prospect Park South", "source": "Neighborhood"}, {"id": 3382, "parentId": 3166, "name": "Queens Village", "source": "Neighborhood"}, {"id": 3754, "parentId": 3737, "name": "<PERSON><PERSON><PERSON>", "source": "Neighborhood"}, {"id": 3755, "parentId": 3737, "name": "Quogue", "source": "Neighborhood"}, {"id": 3472, "parentId": 3168, "name": "Randall Manor", "source": "neighborhood"}, {"id": 3321, "parentId": 3165, "name": "Red Hook", "source": "Neighborhood"}, {"id": 3384, "parentId": 3166, "name": "Rego Park", "source": "Neighborhood"}, {"id": 3756, "parentId": 3737, "name": "Remsenburg", "source": "Neighborhood"}, {"id": 3385, "parentId": 3166, "name": "Richmond Hill", "source": "Neighborhood"}, {"id": 3500, "parentId": 3168, "name": "Richmond Valley", "source": "neighborhood"}, {"id": 3441, "parentId": 3168, "name": "Richmondtown", "source": "neighborhood"}, {"id": 3386, "parentId": 3166, "name": "Ridgewood", "source": "Neighborhood"}, {"id": 3757, "parentId": 3737, "name": "Riverhead", "source": "Neighborhood"}, {"id": 3387, "parentId": 3166, "name": "Rockaway", "source": "Neighborhood"}, {"id": 3686, "parentId": 3166, "name": "Rockaway Beach", "source": "Neighborhood"}, {"id": 3420, "parentId": 3166, "name": "Rockaway Park", "source": "Neighborhood"}, {"id": 3473, "parentId": 3168, "name": "Rosebank", "source": "neighborhood"}, {"id": 3388, "parentId": 3166, "name": "Rosedale", "source": "Neighborhood"}, {"id": 3501, "parentId": 3168, "name": "Rossville", "source": "neighborhood"}, {"id": 3453, "parentId": 3168, "name": "Royal Oak", "source": "neighborhood"}, {"id": 3758, "parentId": 3737, "name": "Sag Harbor", "source": "Neighborhood"}, {"id": 3759, "parentId": 3737, "name": "Sagaponack", "source": "Neighborhood"}, {"id": 3474, "parentId": 3168, "name": "<PERSON>", "source": "neighborhood"}, {"id": 3475, "parentId": 3168, "name": "Saint Pauls", "source": "neighborhood"}, {"id": 3777, "parentId": 3737, "name": "Saltaire", "source": "Neighborhood"}, {"id": 3502, "parentId": 3168, "name": "Sandy Ground", "source": "neighborhood"}, {"id": 3263, "parentId": 3167, "name": "Sc<PERSON>ylerville", "source": "Neighborhood"}, {"id": 3322, "parentId": 3165, "name": "Seagate", "source": "Neighborhood"}, {"id": 3323, "parentId": 3898, "name": "Sheepshead Bay", "source": "Neighborhood"}, {"id": 3760, "parentId": 3737, "name": "Shelter Island", "source": "Neighborhood"}, {"id": 3761, "parentId": 3737, "name": "<PERSON><PERSON><PERSON>", "source": "Neighborhood"}, {"id": 3476, "parentId": 3168, "name": "Shore Acres", "source": "neighborhood"}, {"id": 3477, "parentId": 3168, "name": "Silver Lake", "source": "neighborhood"}, {"id": 3778, "parentId": 3737, "name": "Smithtown", "source": "Neighborhood"}, {"id": 3478, "parentId": 3168, "name": "Snug Harbor", "source": "neighborhood"}, {"id": 3187, "parentId": 3890, "name": "Soho", "source": "Neighborhood"}, {"id": 3264, "parentId": 3167, "name": "Soundview", "source": "Neighborhood"}, {"id": 3442, "parentId": 3168, "name": "South Beach", "source": "neighborhood"}, {"id": 3224, "parentId": 3882, "name": "South Harlem", "source": "Neighborhood"}, {"id": 3390, "parentId": 3166, "name": "South Ozone Park", "source": "Neighborhood"}, {"id": 3486, "parentId": 3168, "name": "South Shore", "source": "neighborhood"}, {"id": 3607, "parentId": 3165, "name": "South Slope", "source": "Neighborhood"}, {"id": 3762, "parentId": 3737, "name": "Southampton", "source": "Neighborhood"}, {"id": 3779, "parentId": 3737, "name": "Southold", "source": "Neighborhood"}, {"id": 3392, "parentId": 3166, "name": "Springfield Gardens", "source": "Neighborhood"}, {"id": 3262, "parentId": 3901, "name": "Spuyten Duyvil", "source": "Neighborhood"}, {"id": 3393, "parentId": 3166, "name": "St. Albans", "source": "Neighborhood"}, {"id": 3665, "parentId": 3168, "name": "St. George", "source": "Neighborhood"}, {"id": 3479, "parentId": 3168, "name": "Stapleton", "source": "neighborhood"}, {"id": 3480, "parentId": 3168, "name": "Stapleton Heights", "source": "neighborhood"}, {"id": 3324, "parentId": 3897, "name": "Starrett City", "source": "Neighborhood"}, {"id": 3331, "parentId": 3846, "name": "Stuyvesant Heights", "source": "Neighborhood"}, {"id": 3188, "parentId": 3169, "name": "Stuyvesant Town/PCV", "source": "Neighborhood"}, {"id": 3394, "parentId": 3166, "name": "Sunnyside", "source": "Neighborhood"}, {"id": 3454, "parentId": 3168, "name": "Sunnyside (Staten Island)", "source": "neighborhood"}, {"id": 3481, "parentId": 3168, "name": "Sunset Hill", "source": "neighborhood"}, {"id": 3325, "parentId": 3165, "name": "Sunset Park", "source": "Neighborhood"}, {"id": 3199, "parentId": 3197, "name": "Sutton Place", "source": "Neighborhood"}, {"id": 3509, "parentId": 3168, "name": "The Teleport", "source": "neighborhood"}, {"id": 3265, "parentId": 3167, "name": "Throgs Neck", "source": "Neighborhood"}, {"id": 3443, "parentId": 3168, "name": "Todt Hill", "source": "neighborhood"}, {"id": 3482, "parentId": 3168, "name": "Tompkinsville", "source": "neighborhood"}, {"id": 3503, "parentId": 3168, "name": "Tottenville", "source": "neighborhood"}, {"id": 3504, "parentId": 3168, "name": "Tottenville Beach", "source": "neighborhood"}, {"id": 3510, "parentId": 3168, "name": "<PERSON>", "source": "neighborhood"}, {"id": 3267, "parentId": 3167, "name": "Tremont", "source": "Neighborhood"}, {"id": 3189, "parentId": 3169, "name": "Tribeca", "source": "Neighborhood"}, {"id": 3200, "parentId": 3197, "name": "Turtle Bay", "source": "Neighborhood"}, {"id": 3192, "parentId": 3889, "name": "Two Bridges", "source": "Neighborhood"}, {"id": 3605, "parentId": 3169, "name": "Union Square Park", "source": "Neighborhood"}, {"id": 3656, "parentId": 3167, "name": "Unionport", "source": "Neighborhood"}, {"id": 3269, "parentId": 3167, "name": "University Heights", "source": "Neighborhood"}, {"id": 3212, "parentId": 3171, "name": "Upper East Side (UES)", "source": "Neighborhood"}, {"id": 3604, "parentId": 3171, "name": "Upper Fifth", "source": "Neighborhood"}, {"id": 3208, "parentId": 3173, "name": "Upper West Side (UWS)", "source": "Neighborhood"}, {"id": 3270, "parentId": 3167, "name": "<PERSON>", "source": "Neighborhood"}, {"id": 3329, "parentId": 3894, "name": "<PERSON>egar Hill", "source": "Neighborhood"}, {"id": 3763, "parentId": 3737, "name": "<PERSON><PERSON><PERSON><PERSON>", "source": "Neighborhood"}, {"id": 3271, "parentId": 3167, "name": "Wakefield", "source": "Neighborhood"}, {"id": 3483, "parentId": 3168, "name": "Ward <PERSON>", "source": "neighborhood"}, {"id": 3220, "parentId": 3883, "name": "Washington Heights", "source": "Neighborhood"}, {"id": 3764, "parentId": 3737, "name": "Water Mill", "source": "Neighborhood"}, {"id": 3295, "parentId": 3893, "name": "Weeksville", "source": "Neighborhood"}, {"id": 3484, "parentId": 3168, "name": "West Brighton", "source": "neighborhood"}, {"id": 3237, "parentId": 3167, "name": "West Farms", "source": "Neighborhood"}, {"id": 3222, "parentId": 3884, "name": "West Harlem", "source": "Neighborhood"}, {"id": 3485, "parentId": 3168, "name": "West New Brighton", "source": "neighborhood"}, {"id": 3506, "parentId": 3168, "name": "West Shore", "source": "neighborhood"}, {"id": 3190, "parentId": 3169, "name": "West Village", "source": "Neighborhood"}, {"id": 3273, "parentId": 3167, "name": "Westchester Square", "source": "Neighborhood"}, {"id": 3272, "parentId": 3167, "name": "Westchester Village", "source": "Neighborhood"}, {"id": 3455, "parentId": 3168, "name": "Westerleigh", "source": "neighborhood"}, {"id": 3765, "parentId": 3737, "name": "Westhampton", "source": "Neighborhood"}, {"id": 3766, "parentId": 3737, "name": "Westhampton Beach", "source": "Neighborhood"}, {"id": 3396, "parentId": 3166, "name": "Whitestone", "source": "Neighborhood"}, {"id": 3274, "parentId": 3167, "name": "Williamsbridge", "source": "Neighborhood"}, {"id": 3326, "parentId": 102, "name": "Williamsburg, North side", "source": "Neighborhood"}, {"id": 119, "parentId": 102, "name": "Williamsburg, South side", "source": "Neighborhood"}, {"id": 3456, "parentId": 3168, "name": "Willowbrook", "source": "neighborhood"}, {"id": 3328, "parentId": 3165, "name": "Windsor Terrace", "source": "Neighborhood"}, {"id": 3340, "parentId": 3896, "name": "Wingate", "source": "Neighborhood"}, {"id": 3397, "parentId": 3166, "name": "Woodhaven", "source": "Neighborhood"}, {"id": 3275, "parentId": 3167, "name": "Woodlawn", "source": "Neighborhood"}, {"id": 3505, "parentId": 3168, "name": "<PERSON>", "source": "neighborhood"}, {"id": 3398, "parentId": 3166, "name": "Woodside", "source": "Neighborhood"}, {"id": 3276, "parentId": 3167, "name": "Woodstock", "source": "Neighborhood"}, {"id": 3213, "parentId": 3171, "name": "Yorkville", "source": "Neighborhood"}]