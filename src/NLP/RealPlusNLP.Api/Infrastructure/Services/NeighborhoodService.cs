﻿using RealPlusNLP.Api.Abstractions.Services;
using RealPlusNLP.Api.Common.Enums;
using RealPlusNLP.Api.Common.Models;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace RealPlusNLP.Api.Infrastructure.Services;

public class NeighborhoodService : INeighborhoodService
{
    private readonly IEnumerable<NeighborhoodDataModel> _neighborhoods;

    public NeighborhoodService()
    {
        _neighborhoods = LoadNeighborhoods();
    }

    public IEnumerable<NeighborhoodModel> Search(string searchTerm, CityAreaType divisionType)
    {
        var ids = _neighborhoods
            .Where(n => n.Name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)
                && n.CityArea == divisionType)
            .Take(5)
            .SelectMany(n => n.ChildrenIds)
            .Distinct();

        return _neighborhoods
            .Where(n => ids.Contains(n.Id))
            .Select(n => new NeighborhoodModel(n.Id, n.Name));
    }

    private static List<NeighborhoodDataModel> LoadNeighborhoods()
    {
        var neighborhoods = JsonSerializer.Deserialize<List<NeighborhoodDataModel>>(
            File.ReadAllText("Infrastructure/Data/neighborhood.json"))!;

        var neighborhoodsLookup = neighborhoods.ToDictionary(i => i.Id);

        // Create a dictionary to store child relationships
        foreach (var item in neighborhoods)
        {
            if (neighborhoodsLookup.TryGetValue(item.ParentId, out NeighborhoodDataModel? value))
            {
                value.ChildrenIds.Add(item.Id);
            }
        }

        // Find leaf nodes
        var leafIds = new HashSet<int>(neighborhoods.Where(i => !neighborhoods.Any(x => x.ParentId == i.Id)).Select(i => i.Id));

        // Update ChildrenIds to contain only leaf nodes
        foreach (var item in neighborhoods)
        {
            if (item.ChildrenIds.Count > 0)
            {
                item.ChildrenIds = GetLeafNodes(item.Id, neighborhoodsLookup, leafIds);
            }
            if (item.ChildrenIds.Count == 0)
            {
                item.ChildrenIds.Add(item.Id);
            }
        }

        return neighborhoods;
    }

    private static List<int> GetLeafNodes(int itemId, Dictionary<int, NeighborhoodDataModel> itemLookup, HashSet<int> leafIds)
    {
        List<int> result = [];
        Queue<int> queue = new(itemLookup[itemId].ChildrenIds);

        while (queue.Count > 0)
        {
            int currentId = queue.Dequeue();
            if (leafIds.Contains(currentId))
            {
                result.Add(currentId);
            }
            else
            {
                foreach (var childId in itemLookup[currentId].ChildrenIds)
                {
                    queue.Enqueue(childId);
                }
            }
        }

        return result;
    }
}

internal class NeighborhoodDataModel
{
    [JsonPropertyName("id")]
    public int Id { get; set; }
    [JsonPropertyName("parentId")]
    public int ParentId { get; set; }
    [JsonPropertyName("name")]
    public string Name { get; set; } = default!;
    [JsonPropertyName("source")]
    public CityAreaType CityArea { get; set; }
    [JsonIgnore]
    public IList<int> ChildrenIds { get; set; } = [];
}